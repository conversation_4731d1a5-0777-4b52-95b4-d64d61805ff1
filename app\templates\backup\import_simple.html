{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-upload"></i> Importer Base de Données</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Attention:</strong> Cette opération remplacera votre base de données actuelle.
                        Une sauvegarde sera créée automatiquement avant l'import.
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="mb-3">
                            <label for="backup_file" class="form-label">Fichier de sauvegarde (.db ou .sqlite)</label>
                            <input type="file" class="form-control" id="backup_file" name="backup_file"
                                   accept=".db,.sqlite" required>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-warning btn-lg" 
                                    onclick="return confirm('Êtes-vous sûr de vouloir remplacer la base de données actuelle?')">
                                <i class="fas fa-upload"></i> Importer
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4 text-center">
                        <a href="{{ url_for('backup_list') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour aux sauvegardes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
