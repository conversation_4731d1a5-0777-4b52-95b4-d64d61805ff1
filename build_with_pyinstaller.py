"""
طريقة بديلة لإنشاء الملف التنفيذي باستخدام PyInstaller
أحياناً يكون أسهل من cx_Freeze
"""

import os
import sys
import subprocess

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller تم تثبيته بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('app', 'app'),
        ('config.py', '.') if os.path.exists('config.py') else None,
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy', 
        'flask_login',
        'flask_wtf',
        'werkzeug',
        'jinja2',
        'wtforms',
        'sqlalchemy',
        'sqlite3'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# تنظيف datas من None
a.datas = [x for x in a.datas if x is not None]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='GestionFormations',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='GestionFormations',
)
"""
    
    with open('gestion_formations.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ ملف .spec تم إنشاؤه")

def build_with_pyinstaller():
    """بناء الملف التنفيذي باستخدام PyInstaller"""
    
    print("🏗️ بناء الملف التنفيذي باستخدام PyInstaller...")
    
    try:
        # إنشاء الملف التنفيذي
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onedir",  # مجلد واحد بدلاً من ملف واحد
            "--windowed",  # بدون نافذة console
            "--name=GestionFormations",
            "--add-data=app;app",
            "--hidden-import=flask",
            "--hidden-import=flask_sqlalchemy",
            "--hidden-import=flask_login",
            "--hidden-import=flask_wtf",
            "--exclude-module=tkinter",
            "run.py"
        ]
        
        # إضافة config.py إذا كان موجوداً
        if os.path.exists("config.py"):
            cmd.insert(-1, "--add-data=config.py;.")
        
        subprocess.check_call(cmd)
        print("✅ البناء اكتمل بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في البناء: {e}")
        return False

def check_result():
    """فحص النتيجة"""
    
    print("\n🔍 فحص النتيجة...")
    
    # البحث عن الملف التنفيذي
    possible_paths = [
        "dist/GestionFormations/GestionFormations.exe",
        "dist/GestionFormations.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"🎉 الملف التنفيذي موجود: {path}")
            
            # حساب الحجم
            size = os.path.getsize(path)
            size_mb = size / (1024 * 1024)
            print(f"📊 الحجم: {size_mb:.1f} MB")
            
            return True
    
    print("❌ الملف التنفيذي غير موجود")
    
    # عرض محتويات مجلد dist
    if os.path.exists("dist"):
        print("\n📁 محتويات مجلد dist:")
        for item in os.listdir("dist"):
            print(f"   - {item}")
    
    return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إنشاء الملف التنفيذي باستخدام PyInstaller")
    print("=" * 60)
    
    # التحقق من الملفات الأساسية
    if not os.path.exists('run.py'):
        print("❌ ملف run.py غير موجود")
        return
    
    if not os.path.exists('app'):
        print("❌ مجلد app غير موجود")
        return
    
    print("✅ الملفات الأساسية موجودة")
    
    # تثبيت PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller متاح")
    except ImportError:
        if not install_pyinstaller():
            return
    
    # بناء الملف التنفيذي
    if build_with_pyinstaller():
        if check_result():
            print("\n🎉 نجح! الملف التنفيذي جاهز")
            print("\n📋 كيفية الاستخدام:")
            print("1. اذهب إلى مجلد dist/GestionFormations/")
            print("2. شغل GestionFormations.exe")
            print("3. البرنامج سيفتح في المتصفح")
        else:
            print("\n❌ فشل في إنشاء الملف التنفيذي")
    
    print("\n" + "=" * 60)
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
