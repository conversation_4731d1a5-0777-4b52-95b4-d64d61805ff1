@echo off
chcp 65001 >nul
title GESTION DES FORMATIONS - Créateur d'Installateur

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 GESTION DES FORMATIONS                       ║
echo ║                 Créateur d'Installateur                     ║
echo ║                                                              ║
echo ║                 Développé par ABOULFADEL.A                   ║
echo ║                      Version 1.0.0                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Vérification de l'environnement...
echo.

REM Vérifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERREUR: Python n'est pas installé ou pas dans le PATH
    echo.
    echo 📥 Téléchargez Python depuis: https://www.python.org/downloads/
    echo ⚠️  Assurez-vous de cocher "Add Python to PATH" lors de l'installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python détecté
python --version

echo.
echo 🔧 Préparation du build...
python prepare_build.py

echo.
echo 📦 Installation des dépendances...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ Erreur lors de l'installation des dépendances
    pause
    exit /b 1
)

echo.
echo 🏗️ Création de l'exécutable...
python setup.py build

if errorlevel 1 (
    echo ❌ Erreur lors de la création de l'exécutable
    pause
    exit /b 1
)

echo.
echo 📋 Génération des fichiers d'installation...
python build_installer.py

echo.
echo ✅ BUILD TERMINÉ AVEC SUCCÈS!
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      RÉSULTATS                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

if exist "build\exe.win-amd64-3.11\GestionFormations.exe" (
    echo ✅ Exécutable créé: build\exe.win-amd64-3.11\GestionFormations.exe
) else if exist "build\exe.win32-3.11\GestionFormations.exe" (
    echo ✅ Exécutable créé: build\exe.win32-3.11\GestionFormations.exe
) else (
    echo ❌ Exécutable non trouvé - vérifiez les erreurs ci-dessus
)

if exist "installer_script.iss" (
    echo ✅ Script Inno Setup créé: installer_script.iss
) else (
    echo ❌ Script Inno Setup non créé
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   ÉTAPES SUIVANTES                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1. 📥 Téléchargez et installez Inno Setup:
echo    https://jrsoftware.org/isinfo.php
echo.
echo 2. 🔨 Compilez l'installateur avec la commande:
echo    iscc installer_script.iss
echo.
echo 3. 📦 L'installateur final sera créé dans:
echo    dist\installer\GestionFormations_Setup_v1.0.0.exe
echo.
echo 4. 🧪 Testez l'installateur sur une machine propre
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 INFORMATIONS TECHNIQUES                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 Multi-utilisateurs: Oui (réseau LAN)
echo 🖥️  Compatibilité: Windows 7/8/8.1/10/11
echo 🏗️  Architecture: 32-bit et 64-bit
echo 📊 Base de données: SQLite (incluse)
echo 🔐 Sécurité: CSRF, Sessions, Logs
echo.
echo © 2024 ABOULFADEL.A - Tous droits réservés
echo.
pause
