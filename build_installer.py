"""
Script pour créer un installateur Windows avec Inno Setup
Génère automatiquement le script .iss et compile l'installateur
"""

import os
import subprocess
import sys

def create_inno_script():
    """Créer le script Inno Setup"""
    
    script_content = """
; Script Inno Setup pour GESTION DES FORMATIONS
; Créé automatiquement par build_installer.py

#define MyAppName "GESTION DES FORMATIONS"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "ABOULFADEL.A"
#define MyAppURL "https://aboulfadel.com"
#define MyAppExeName "GestionFormations.exe"
#define MyAppAssocName "Formation Database"
#define MyAppAssocExt ".fdb"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\\{#MyAppName}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoAfterFile=README.txt
OutputDir=dist\\installer
OutputBaseFilename=GestionFormations_Setup_v{#MyAppVersion}
SetupIconFile=app\\static\\images\\Formation-continue-1024x1024.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "french"; MessagesFile: "compiler:Languages\\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "build\\exe.win-amd64-3.11\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "app\\static\\images\\Formation-continue-1024x1024.ico"; DestDir: "{app}"; Flags: ignoreversion

[Registry]
Root: HKA; Subkey: "Software\\Classes\\{#MyAppAssocExt}\\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\\Classes\\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\\Classes\\{#MyAppAssocKey}\\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\\Classes\\{#MyAppAssocKey}\\shell\\open\\command"; ValueType: string; ValueName: ""; ValueData: "\"{app}\\{#MyAppExeName}\" \"%1\""
Root: HKA; Subkey: "Software\\Classes\\Applications\\{#MyAppExeName}\\SupportedTypes"; ValueType: string; ValueName: ".myp"; ValueData: ""

[Icons]
Name: "{group}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"
Name: "{group}\\{cm:ProgramOnTheWeb,{#MyAppName}}"; Filename: "{#MyAppURL}"
Name: "{group}\\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#SetupSetting('AppName')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}\\backups"
Type: filesandordirs; Name: "{app}\\logs"
Type: filesandordirs; Name: "{app}\\uploads"

[Code]
procedure InitializeWizard;
begin
  WizardForm.WelcomeLabel1.Caption := 'Bienvenue dans l''assistant d''installation de';
  WizardForm.WelcomeLabel2.Caption := 'GESTION DES FORMATIONS' + #13#10 + 'Version 1.0.0' + #13#10 + #13#10 + 'Développé par ABOULFADEL.A';
end;
"""
    
    with open('installer_script.iss', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Script Inno Setup créé: installer_script.iss")

def create_license_file():
    """Créer le fichier de licence"""
    license_content = """LICENCE D'UTILISATION - GESTION DES FORMATIONS

© 2024 ABOULFADEL.A - Tous droits réservés

Ce logiciel est la propriété exclusive de ABOULFADEL.A.

DROITS ACCORDÉS:
- Utilisation du logiciel sur un nombre illimité d'ordinateurs au sein de votre organisation
- Installation sur réseau local (LAN) pour usage multi-utilisateurs
- Création de sauvegardes pour usage interne

RESTRICTIONS:
- Interdiction de redistribution, revente ou location du logiciel
- Interdiction de modification, décompilation ou ingénierie inverse
- Interdiction d'utilisation à des fins commerciales externes

SUPPORT:
Pour toute assistance technique, contactez ABOULFADEL.A

Le logiciel est fourni "en l'état" sans garantie expresse ou implicite.
"""
    
    with open('LICENSE.txt', 'w', encoding='utf-8') as f:
        f.write(license_content)
    
    print("✅ Fichier de licence créé: LICENSE.txt")

def create_readme_file():
    """Créer le fichier README pour l'installateur"""
    readme_content = """GESTION DES FORMATIONS - Guide d'installation

Félicitations ! Vous avez installé avec succès GESTION DES FORMATIONS v1.0.0

PREMIÈRE UTILISATION:
1. Lancez le programme depuis le menu Démarrer ou le raccourci bureau
2. Le programme s'ouvrira dans votre navigateur par défaut
3. Utilisez les identifiants par défaut:
   - Nom d'utilisateur: admin
   - Mot de passe: admin123

CONFIGURATION RÉSEAU (Multi-utilisateurs):
1. Sur l'ordinateur serveur (où le programme est installé):
   - Notez l'adresse IP de cet ordinateur
   - Assurez-vous que le pare-feu autorise le port 5000

2. Sur les autres ordinateurs du réseau:
   - Ouvrez un navigateur web
   - Tapez: http://[IP_DU_SERVEUR]:5000
   - Exemple: http://*************:5000

DOSSIERS IMPORTANTS:
- Sauvegardes: [Dossier d'installation]\\backups
- Fichiers uploadés: [Dossier d'installation]\\uploads
- Base de données: [Dossier d'installation]\\app\\app.db

SUPPORT TECHNIQUE:
Contactez ABOULFADEL.A pour toute assistance

© 2024 ABOULFADEL.A - Tous droits réservés
"""
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ Fichier README créé: README.txt")

def main():
    """Fonction principale"""
    print("🚀 Création de l'installateur GESTION DES FORMATIONS")
    print("=" * 60)
    
    # Créer les fichiers nécessaires
    create_license_file()
    create_readme_file()
    create_inno_script()
    
    print("\n📋 ÉTAPES SUIVANTES:")
    print("1. Installez cx_Freeze: pip install cx_Freeze")
    print("2. Créez l'exécutable: python setup.py build")
    print("3. Installez Inno Setup depuis: https://jrsoftware.org/isinfo.php")
    print("4. Compilez l'installateur avec: iscc installer_script.iss")
    print("\n✨ L'installateur sera créé dans: dist\\installer\\")

if __name__ == "__main__":
    main()
