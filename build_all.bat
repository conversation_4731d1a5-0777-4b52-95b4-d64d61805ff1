@echo off
echo ========================================
echo GESTION DES FORMATIONS - Build Script
echo Developpe par ABOULFADEL.A
echo ========================================
echo.

echo Etape 1: Verification de Python...
python --version
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    pause
    exit /b 1
)

echo.
echo Etape 2: Installation des dependances...
pip install cx_Freeze
pip install -r requirements.txt

echo.
echo Etape 3: Creation des fichiers d'installation...
python build_installer.py

echo.
echo Etape 4: Creation de l'executable...
python setup.py build

echo.
echo Etape 5: Verification des fichiers...
if exist "build\exe.win-amd64-3.11\GestionFormations.exe" (
    echo ✓ Executable cree avec succes
) else (
    echo ✗ Erreur lors de la creation de l'executable
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD TERMINE AVEC SUCCES!
echo ========================================
echo.
echo Fichiers crees:
echo - Executable: build\exe.win-amd64-3.11\GestionFormations.exe
echo - Script Inno Setup: installer_script.iss
echo.
echo PROCHAINES ETAPES:
echo 1. Installez Inno Setup: https://jrsoftware.org/isinfo.php
echo 2. Executez: iscc installer_script.iss
echo 3. L'installateur sera dans: dist\installer\
echo.
pause
