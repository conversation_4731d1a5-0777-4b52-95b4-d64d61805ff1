<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{% endblock %} - Gestion des Formations</title>
    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Moment.js for date formatting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/fr.min.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        #wrapper {
            display: flex;
        }
        #sidebar-wrapper {
            min-height: 100vh;
            width: 250px;
            background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
            color: white;
            box-shadow: 3px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        #page-content-wrapper {
            flex: 1;
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar-heading {
            padding: 20px;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 10px;
        }
        .list-group-item {
            background-color: transparent;
            color: #e0e0e0;
            border: none;
            padding: 15px 20px;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        .list-group-item:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left: 3px solid #007bff;
        }
        .list-group-item-danger {
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 3px solid #e74c3c !important;
        }
        .list-group-item-danger:hover {
            background-color: rgba(231, 76, 60, 0.2);
            color: white;
            border-left: 3px solid #c0392b !important;
        }
        .sidebar-divider {
            height: 1px;
            background: rgba(255,255,255,0.1);
            margin: 10px 20px;
        }
        .list-group-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }
        .btn {
            border-radius: 5px;
            padding: 8px 16px;
        }
        .table {
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border-radius: 5px;
        }
        .table thead th {
            background-color: #f8f9fa;
            border-top: none;
        }
    </style>
</head>
<body>
    {% if not current_user.is_authenticated and request.endpoint == 'login' %}
        <!-- Show only the main content for login page -->
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            {% block login_content %}{% endblock %}
        </div>
    {% else %}
        <!-- Show full layout with sidebar for authenticated users -->
        <div id="wrapper">
            <!-- Sidebar -->
            <div id="sidebar-wrapper">
                <div class="sidebar-heading">
                    <i class="fas fa-graduation-cap"></i> GESTION DES FORMATIONS
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('dashboard') }}" class="list-group-item">
                        <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                    </a>
                    <a href="{{ url_for('fiches_inscription') }}" class="list-group-item">
                        <i class="fas fa-file-alt"></i> Fiches d'Inscription
                    </a>
                    <a href="{{ url_for('eligibilite_ofppt') }}" class="list-group-item">
                        <i class="fas fa-check-circle"></i> Éligibilité OFPPT
                    </a>
                    <a href="{{ url_for('dossiers_techniques') }}" class="list-group-item">
                        <i class="fas fa-folder"></i> Dossiers Techniques
                    </a>
                    <a href="{{ url_for('remboursements') }}" class="list-group-item">
                        <i class="fas fa-file-invoice-dollar"></i> Remboursements
                    </a>
                    <a href="{{ url_for('organismes') }}" class="list-group-item">
                        <i class="fas fa-building"></i> Organismes
                    </a>
                    <a href="{{ url_for('formateurs') }}" class="list-group-item">
                        <i class="fas fa-chalkboard-teacher"></i> Formateurs
                    </a>
                    <a href="{{ url_for('agenda_formateurs') }}" class="list-group-item">
                        <i class="fas fa-calendar-alt"></i> Agenda des Formateurs
                    </a>
                    <a href="{{ url_for('domaines_themes') }}" class="list-group-item">
                        <i class="fas fa-sitemap"></i> Domaines et Thèmes
                    </a>
                    {% if current_user.is_admin %}
                    <a href="{{ url_for('users') }}" class="list-group-item">
                        <i class="fas fa-user-cog"></i> Utilisateurs
                    </a>
                    <div class="sidebar-divider"></div>
                    <a href="{{ url_for('company_info') }}" class="list-group-item">
                        <i class="fas fa-building"></i> Informations Société
                    </a>
                    <a href="{{ url_for('activity_log') }}" class="list-group-item">
                        <i class="fas fa-history"></i> Journal d'Activité
                    </a>
                    <a href="{{ url_for('backup_settings') }}" class="list-group-item">
                        <i class="fas fa-database"></i> Sauvegarde
                    </a>
                    {% endif %}


                </div>
            </div>

            <!-- Page Content -->
            <div id="page-content-wrapper">
                <!-- Navbar -->
                <nav class="navbar navbar-expand-lg navbar-dark">
                    <div class="container-fluid">
                        <button class="btn btn-link" id="menu-toggle">
                            <i class="fas fa-bars text-white"></i>
                        </button>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <!-- Espace pour le titre de la page -->
                            <span class="navbar-text mx-auto">
                                {{ title if title else 'Gestion des Formations' }}
                            </span>

                            <ul class="navbar-nav ms-auto">
                                {% if current_user.is_authenticated %}
                                    <li class="nav-item dropdown">
                                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-user"></i> {{ current_user.username }}
                                        </a>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-user-cog"></i> Profil
                                            </a></li>
                                            <li><a class="dropdown-item" href="{{ url_for('change_password') }}">
                                                <i class="fas fa-key"></i> تغيير كلمة المرور
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                                <i class="fas fa-sign-out-alt"></i> Déconnexion
                                            </a></li>
                                        </ul>
                                    </li>
                                {% else %}
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('login') }}">
                                            <i class="fas fa-sign-in-alt"></i> Connexion
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </nav>

                <!-- Main Content -->
                <div class="container-fluid p-4">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.getElementById("menu-toggle").addEventListener("click", function(e) {
            e.preventDefault();
            document.getElementById("wrapper").classList.toggle("toggled");
        });

        // Initialize all tooltips
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });

        // Initialize all popovers
        $(function () {
            $('[data-toggle="popover"]').popover();
        });
    </script>

    <!-- Delete Handler Script -->
    <script src="{{ url_for('static', filename='js/delete-handler.js') }}"></script>
</body>
</html>
