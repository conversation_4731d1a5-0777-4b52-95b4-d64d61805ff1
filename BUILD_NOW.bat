@echo off
chcp 65001 >nul
title إنشاء الملف التنفيذي

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 GESTION DES FORMATIONS                       ║
echo ║                   إنشاء الملف التنفيذي                      ║
echo ║                                                              ║
echo ║                 ABOULFADEL.A تطوير                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص النظام أولاً...
python check_system.py

echo.
echo ⏳ الآن سنقوم بإنشاء الملف التنفيذي...
pause

echo.
echo 🏗️ بدء عملية البناء...
python build_simple_exe.py

echo.
echo 🔍 فحص النتيجة...

if exist "build\exe.win-amd64-3.11\GestionFormations.exe" (
    echo ✅ تم العثور على الملف التنفيذي: build\exe.win-amd64-3.11\GestionFormations.exe
    goto :success
)

if exist "build\exe.win32-3.11\GestionFormations.exe" (
    echo ✅ تم العثور على الملف التنفيذي: build\exe.win32-3.11\GestionFormations.exe
    goto :success
)

if exist "build\exe.win-amd64-3.10\GestionFormations.exe" (
    echo ✅ تم العثور على الملف التنفيذي: build\exe.win-amd64-3.10\GestionFormations.exe
    goto :success
)

if exist "build\exe.win32-3.10\GestionFormations.exe" (
    echo ✅ تم العثور على الملف التنفيذي: build\exe.win32-3.10\GestionFormations.exe
    goto :success
)

REM البحث في جميع مجلدات exe
for /d %%i in (build\exe.*) do (
    if exist "%%i\GestionFormations.exe" (
        echo ✅ تم العثور على الملف التنفيذي: %%i\GestionFormations.exe
        goto :success
    )
)

echo ❌ لم يتم العثور على الملف التنفيذي
echo.
echo 💡 الحلول المقترحة:
echo 1. تأكد من تثبيت cx_Freeze: pip install cx_Freeze
echo 2. تأكد من وجود ملف run.py في المجلد الحالي
echo 3. شغل python check_system.py للتشخيص
echo 4. أعد تشغيل هذا الملف
goto :end

:success
echo.
echo 🎉 نجح! تم إنشاء الملف التنفيذي بنجاح
echo.
echo 📋 كيفية الاستخدام:
echo 1. اذهب إلى مجلد build\exe.xxx\
echo 2. شغل GestionFormations.exe
echo 3. البرنامج سيفتح في المتصفح
echo.
echo 💡 للتوزيع على أجهزة أخرى:
echo - انسخ كامل مجلد build\exe.xxx\
echo - ليس فقط ملف .exe
echo.
echo 🌐 للاستخدام متعدد المستخدمين:
echo - شغل البرنامج على جهاز واحد (الخادم)
echo - الأجهزة الأخرى تفتح المتصفح وتذهب إلى عنوان IP الخادم

:end
echo.
pause
