"""
Script de préparation pour le build
Convertit l'image en icône et prépare tous les fichiers nécessaires
"""

import os
import shutil
from PIL import Image

def convert_image_to_ico():
    """Convertir l'image PNG en fichier ICO"""
    
    image_path = "app/static/images/Formation-continue-1024x1024.png"
    ico_path = "app/static/images/Formation-continue-1024x1024.ico"
    
    if not os.path.exists(image_path):
        print(f"❌ Image source introuvable: {image_path}")
        # Créer une icône par défaut
        create_default_icon(ico_path)
        return
    
    try:
        # Ouvrir l'image
        img = Image.open(image_path)
        
        # Convertir en RGBA si nécessaire
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Redimensionner pour les tailles d'icône standard
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        
        # Sauvegarder en ICO
        img.save(ico_path, format='ICO', sizes=sizes)
        print(f"✅ Icône créée: {ico_path}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la conversion: {e}")
        create_default_icon(ico_path)

def create_default_icon(ico_path):
    """Créer une icône par défaut si l'image source n'existe pas"""
    try:
        # Créer une image simple avec du texte
        img = Image.new('RGBA', (256, 256), (0, 100, 200, 255))
        
        # Sauvegarder en ICO
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        img.save(ico_path, format='ICO', sizes=sizes)
        print(f"✅ Icône par défaut créée: {ico_path}")
        
    except Exception as e:
        print(f"❌ Impossible de créer l'icône par défaut: {e}")

def update_run_py():
    """Mettre à jour run.py pour utiliser la configuration de production"""
    
    run_content = '''"""
GESTION DES FORMATIONS - Point d'entrée principal
Développé par ABOULFADEL.A
"""

import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Utiliser la configuration de production
os.environ['FLASK_ENV'] = 'production'

# Importer et démarrer l'application
from run_server import main

if __name__ == "__main__":
    main()
'''
    
    with open('run.py', 'w', encoding='utf-8') as f:
        f.write(run_content)
    
    print("✅ run.py mis à jour pour la production")

def create_requirements_txt():
    """Créer/mettre à jour requirements.txt"""
    
    requirements = """Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1
Werkzeug==2.3.7
Jinja2==3.1.2
SQLAlchemy==2.0.21
Pillow==10.0.1
cx_Freeze==6.15.10
"""
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements)
    
    print("✅ requirements.txt créé/mis à jour")

def check_dependencies():
    """Vérifier que toutes les dépendances sont installées"""
    
    try:
        import flask
        print(f"✅ Flask {flask.__version__}")
    except ImportError:
        print("❌ Flask non installé")
    
    try:
        import PIL
        print(f"✅ Pillow {PIL.__version__}")
    except ImportError:
        print("❌ Pillow non installé - requis pour la conversion d'icône")
    
    try:
        import cx_Freeze
        print(f"✅ cx_Freeze {cx_Freeze.__version__}")
    except ImportError:
        print("❌ cx_Freeze non installé - requis pour créer l'exécutable")

def main():
    """Fonction principale"""
    print("🔧 PRÉPARATION DU BUILD - GESTION DES FORMATIONS")
    print("=" * 60)
    
    # Vérifier les dépendances
    print("\n📋 Vérification des dépendances:")
    check_dependencies()
    
    # Créer requirements.txt
    print("\n📄 Création des fichiers de configuration:")
    create_requirements_txt()
    
    # Convertir l'image en icône
    print("\n🎨 Conversion de l'icône:")
    convert_image_to_ico()
    
    # Mettre à jour run.py
    print("\n⚙️ Configuration de production:")
    update_run_py()
    
    print("\n✅ PRÉPARATION TERMINÉE!")
    print("\nÉtapes suivantes:")
    print("1. Exécutez: pip install -r requirements.txt")
    print("2. Exécutez: build_all.bat")
    print("3. Installez Inno Setup et compilez l'installateur")

if __name__ == "__main__":
    main()
