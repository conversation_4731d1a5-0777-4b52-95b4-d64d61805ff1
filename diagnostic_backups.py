"""
Script de diagnostic pour les sauvegardes
Vérifie l'état des fichiers de sauvegarde et répare les chemins
"""

import os
import sys
from app import create_app
from app.models import BackupLog

def check_backup_files():
    """Vérifier l'état de tous les fichiers de sauvegarde"""
    
    app = create_app()
    
    with app.app_context():
        print("🔍 DIAGNOSTIC DES SAUVEGARDES")
        print("=" * 50)
        
        backups = BackupLog.query.all()
        
        if not backups:
            print("❌ Aucune sauvegarde trouvée dans la base de données")
            return
        
        print(f"📊 Total des sauvegardes: {len(backups)}")
        print()
        
        # Vérifier les dossiers de sauvegarde
        backup_dirs = ['backups', 'app/backups', '../backups']
        existing_dirs = []
        
        for backup_dir in backup_dirs:
            if os.path.exists(backup_dir):
                existing_dirs.append(backup_dir)
                files_count = len([f for f in os.listdir(backup_dir) if f.endswith(('.db', '.zip'))])
                print(f"📁 {backup_dir}: {files_count} fichiers")
        
        if not existing_dirs:
            print("❌ Aucun dossier de sauvegarde trouvé!")
            print("💡 Créez le dossier 'backups' dans le répertoire principal")
            return
        
        print()
        print("🔍 Vérification des sauvegardes individuelles:")
        print("-" * 50)
        
        found_files = 0
        missing_files = 0
        repaired_files = 0
        
        for backup in backups:
            status = "❌"
            message = "Fichier introuvable"
            
            # Vérifier le chemin actuel
            if backup.chemin_fichier and os.path.exists(backup.chemin_fichier):
                status = "✅"
                message = "OK"
                found_files += 1
            else:
                # Chercher dans les dossiers de sauvegarde
                for backup_dir in existing_dirs:
                    potential_path = os.path.join(backup_dir, backup.nom_fichier)
                    if os.path.exists(potential_path):
                        status = "🔧"
                        message = f"Trouvé dans {backup_dir} - Réparation..."
                        
                        # Réparer le chemin
                        backup.chemin_fichier = os.path.abspath(potential_path)
                        repaired_files += 1
                        found_files += 1
                        break
                else:
                    missing_files += 1
            
            print(f"{status} {backup.nom_fichier} - {message}")
        
        # Sauvegarder les réparations
        if repaired_files > 0:
            from app import db
            db.session.commit()
            print(f"\n🔧 {repaired_files} chemins réparés et sauvegardés")
        
        print()
        print("📊 RÉSUMÉ:")
        print(f"✅ Fichiers trouvés: {found_files}")
        print(f"❌ Fichiers manquants: {missing_files}")
        print(f"🔧 Chemins réparés: {repaired_files}")
        
        if missing_files > 0:
            print("\n💡 SUGGESTIONS:")
            print("- Vérifiez que les fichiers n'ont pas été déplacés")
            print("- Recréez les sauvegardes manquantes")
            print("- Vérifiez les permissions des dossiers")

def list_physical_files():
    """Lister tous les fichiers physiques de sauvegarde"""
    
    print("\n📁 FICHIERS PHYSIQUES TROUVÉS:")
    print("=" * 50)
    
    backup_dirs = ['backups', 'app/backups', '../backups']
    
    for backup_dir in backup_dirs:
        if os.path.exists(backup_dir):
            print(f"\n📂 Dossier: {backup_dir}")
            files = [f for f in os.listdir(backup_dir) if f.endswith(('.db', '.zip'))]
            
            if files:
                for file in sorted(files):
                    file_path = os.path.join(backup_dir, file)
                    size = os.path.getsize(file_path)
                    size_mb = size / (1024 * 1024)
                    print(f"  📄 {file} ({size_mb:.2f} MB)")
            else:
                print("  (vide)")

def main():
    """Fonction principale"""
    
    print("🔧 DIAGNOSTIC DES SAUVEGARDES - GESTION DES FORMATIONS")
    print("Développé par ABOULFADEL.A")
    print("=" * 60)
    
    try:
        check_backup_files()
        list_physical_files()
        
        print("\n✅ Diagnostic terminé!")
        print("\nSi des problèmes persistent:")
        print("1. Vérifiez les permissions des dossiers")
        print("2. Recréez une sauvegarde test")
        print("3. Contactez le support technique")
        
    except Exception as e:
        print(f"\n❌ Erreur lors du diagnostic: {e}")
        print("Vérifiez que l'application est correctement configurée")

if __name__ == "__main__":
    main()
