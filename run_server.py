"""
Script de démarrage pour le serveur multi-utilisateurs
Permet l'accès réseau pour plusieurs utilisateurs simultanés
"""

import os
import sys
import socket
import webbrowser
import threading
import time
from app import create_app

def get_local_ip():
    """Obtenir l'adresse IP locale"""
    try:
        # Créer une socket pour obtenir l'IP locale
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def open_browser(url):
    """Ouvrir le navigateur après un délai"""
    time.sleep(2)  # Attendre que le serveur démarre
    webbrowser.open(url)

def main():
    """Fonction principale"""
    print("=" * 60)
    print("🚀 GESTION DES FORMATIONS - Serveur Multi-utilisateurs")
    print("   Développé par ABOULFADEL.A")
    print("=" * 60)
    
    # Créer l'application Flask
    app = create_app()
    
    # Obtenir l'IP locale
    local_ip = get_local_ip()
    port = 5000
    
    print(f"\n📡 Serveur démarré sur:")
    print(f"   • Local:  http://127.0.0.1:{port}")
    print(f"   • Réseau: http://{local_ip}:{port}")
    print(f"\n👥 Accès multi-utilisateurs:")
    print(f"   Les autres utilisateurs peuvent se connecter via:")
    print(f"   http://{local_ip}:{port}")
    print(f"\n🔐 Identifiants par défaut:")
    print(f"   Nom d'utilisateur: admin")
    print(f"   Mot de passe: admin123")
    print(f"\n⚠️  Pour arrêter le serveur: Ctrl+C")
    print("=" * 60)
    
    # Ouvrir le navigateur automatiquement
    browser_thread = threading.Thread(target=open_browser, args=(f"http://127.0.0.1:{port}",))
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # Démarrer le serveur Flask
        app.run(
            host='0.0.0.0',  # Écouter sur toutes les interfaces
            port=port,
            debug=False,
            threaded=True,  # Support multi-threading pour plusieurs utilisateurs
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n\n🛑 Serveur arrêté par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors du démarrage du serveur: {e}")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
