{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-unlock-alt"></i> Mot de passe oublié</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle"></i>
                        Pour récupérer votre mot de passe, veuillez saisir votre nom d'utilisateur et l'adresse e-mail associée à votre compte, puis choisissez un nouveau mot de passe.
                    </div>
                    
                    <form method="POST" action="">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-group mb-3">
                            {{ form.username.label(class="form-control-label") }}
                            {{ form.username(class="form-control", placeholder="Saisissez votre nom d'utilisateur") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control", placeholder="Saisissez votre adresse e-mail") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            {{ form.new_password.label(class="form-control-label") }}
                            {{ form.new_password(class="form-control", placeholder="Saisissez le nouveau mot de passe") }}
                            {% if form.new_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.new_password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Le mot de passe doit contenir au moins 6 caractères</small>
                        </div>

                        <div class="form-group mb-4">
                            {{ form.confirm_password.label(class="form-control-label") }}
                            {{ form.confirm_password(class="form-control", placeholder="Confirmez le nouveau mot de passe") }}
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-warning btn-lg text-dark") }}
                        </div>
                    </form>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('login') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à la connexion
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    padding: 20px;
}

.card-body {
    padding: 30px;
}

.form-control {
    border-radius: 10px;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255,193,7,.25);
    background-color: #fff;
}

.btn {
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529 !important;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255,193,7,0.3);
    color: #212529 !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108,117,125,0.3);
}

.form-control-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.text-muted {
    color: #6c757d !important;
    font-size: 0.875rem;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    border-radius: 10px;
}
</style>
{% endblock %}
