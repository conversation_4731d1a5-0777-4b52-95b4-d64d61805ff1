"""
سكريبت تشخيص النظام لمعرفة سبب عدم إنشاء الملف التنفيذي
"""

import sys
import os
import subprocess

def check_python():
    """فحص Python"""
    print("🐍 فحص Python:")
    print(f"   الإصدار: {sys.version}")
    print(f"   المسار: {sys.executable}")
    print(f"   البت: {sys.maxsize > 2**32 and '64-bit' or '32-bit'}")

def check_packages():
    """فحص الحزم المطلوبة"""
    print("\n📦 فحص الحزم:")
    
    packages = ['cx_Freeze', 'flask', 'flask_sqlalchemy', 'flask_login', 'flask_wtf']
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}: مثبت")
        except ImportError:
            print(f"   ❌ {package}: غير مثبت")

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات:")
    
    required_files = ['run.py', 'app/', 'config.py']
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}: موجود")
        else:
            print(f"   ❌ {file}: مفقود")

def check_build_folder():
    """فحص مجلد البناء"""
    print("\n🏗️ فحص مجلد البناء:")
    
    if os.path.exists('build'):
        print("   📁 مجلد build موجود:")
        for item in os.listdir('build'):
            print(f"      - {item}")
            if item.startswith('exe.'):
                exe_path = os.path.join('build', item, 'GestionFormations.exe')
                if os.path.exists(exe_path):
                    print(f"      ✅ الملف التنفيذي موجود: {exe_path}")
                else:
                    print(f"      ❌ الملف التنفيذي مفقود في {item}")
    else:
        print("   ❌ مجلد build غير موجود")

def test_simple_build():
    """اختبار بناء بسيط"""
    print("\n🧪 اختبار بناء بسيط:")
    
    try:
        from cx_Freeze import setup, Executable
        print("   ✅ cx_Freeze يعمل")
        
        # إنشاء ملف اختبار بسيط
        test_script = """
print("Hello World!")
input("اضغط Enter للخروج...")
"""
        
        with open('test_simple.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("   📝 ملف اختبار تم إنشاؤه")
        
        # محاولة بناء الملف الاختبار
        print("   🔨 محاولة بناء ملف اختبار...")
        
        executable = Executable(
            script="test_simple.py",
            target_name="test.exe"
        )
        
        build_options = {
            "packages": [],
            "excludes": ["tkinter"],
            "optimize": 1
        }
        
        # تشغيل البناء
        old_argv = sys.argv
        sys.argv = ['setup.py', 'build']
        
        try:
            setup(
                name="Test",
                version="1.0",
                options={"build_exe": build_options},
                executables=[executable]
            )
            print("   ✅ البناء نجح!")
        except Exception as e:
            print(f"   ❌ البناء فشل: {e}")
        finally:
            sys.argv = old_argv
            
        # تنظيف
        if os.path.exists('test_simple.py'):
            os.remove('test_simple.py')
            
    except Exception as e:
        print(f"   ❌ خطأ في cx_Freeze: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 تشخيص النظام - GESTION DES FORMATIONS")
    print("=" * 60)
    
    check_python()
    check_packages()
    check_files()
    check_build_folder()
    test_simple_build()
    
    print("\n" + "=" * 60)
    print("📋 التوصيات:")
    
    if not os.path.exists('run.py'):
        print("❌ ملف run.py مفقود - تأكد من أنك في المجلد الصحيح")
    
    try:
        import cx_Freeze
        print("✅ cx_Freeze مثبت بشكل صحيح")
    except ImportError:
        print("❌ أعد تثبيت cx_Freeze: pip install --upgrade cx_Freeze")
    
    if not os.path.exists('build'):
        print("💡 لم يتم إنشاء مجلد build - هناك خطأ في عملية البناء")
    
    print("\n🚀 الخطوات التالية:")
    print("1. تأكد من حل جميع المشاكل أعلاه")
    print("2. شغل: python build_simple_exe.py")
    print("3. إذا استمرت المشكلة، أرسل نتائج هذا التشخيص")

if __name__ == "__main__":
    main()
