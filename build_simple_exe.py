"""
سكريبت بناء مبسط جداً لإنشاء الملف التنفيذي
"""

import sys
import os

print("🔧 بناء الملف التنفيذي - نسخة مبسطة")
print("=" * 50)

# التحقق من الملفات الأساسية
if not os.path.exists('run.py'):
    print("❌ خطأ: ملف run.py غير موجود")
    print("💡 تأكد من أنك في مجلد المشروع الصحيح")
    input("اضغط Enter للخروج...")
    sys.exit(1)

if not os.path.exists('app'):
    print("❌ خطأ: مجلد app غير موجود")
    input("اضغط Enter للخروج...")
    sys.exit(1)

print("✅ الملفات الأساسية موجودة")

# التحقق من cx_Freeze
try:
    from cx_Freeze import setup, Executable
    print("✅ cx_Freeze متاح")
except ImportError:
    print("❌ cx_Freeze غير مثبت")
    print("💡 شغل: pip install cx_Freeze")
    input("اضغط Enter للخروج...")
    sys.exit(1)

# إعداد البناء
print("\n🏗️ بدء عملية البناء...")

# خيارات البناء المبسطة
build_exe_options = {
    "packages": [
        "flask", "werkzeug", "jinja2", "sqlite3", "os", "sys"
    ],
    "include_files": [
        ("app", "app")
    ],
    "excludes": [
        "tkinter", "unittest", "email", "http", "urllib", "xml"
    ]
}

# إضافة config.py إذا كان موجوداً
if os.path.exists("config.py"):
    build_exe_options["include_files"].append(("config.py", "config.py"))

# الملف التنفيذي
executable = Executable(
    script="run.py",
    target_name="GestionFormations.exe"
)

print("📦 تكوين البناء...")

# حفظ sys.argv الأصلي
original_argv = sys.argv[:]

try:
    # تعديل sys.argv للبناء
    sys.argv = ['setup.py', 'build']
    
    print("🔨 بدء التجميع...")
    
    setup(
        name="GESTION DES FORMATIONS",
        version="1.0.0",
        description="نظام إدارة التكوين",
        options={"build_exe": build_exe_options},
        executables=[executable]
    )
    
    print("✅ التجميع اكتمل!")
    
except Exception as e:
    print(f"❌ خطأ في التجميع: {e}")
    print("\n🔍 تفاصيل الخطأ:")
    import traceback
    traceback.print_exc()
    
finally:
    # استعادة sys.argv
    sys.argv = original_argv

# التحقق من النتيجة
print("\n🔍 فحص النتيجة...")

build_found = False
exe_found = False

if os.path.exists("build"):
    print("✅ مجلد build تم إنشاؤه")
    build_found = True
    
    # البحث عن الملف التنفيذي
    for item in os.listdir("build"):
        if item.startswith("exe."):
            exe_dir = os.path.join("build", item)
            exe_path = os.path.join(exe_dir, "GestionFormations.exe")
            
            if os.path.exists(exe_path):
                print(f"🎉 الملف التنفيذي موجود: {exe_path}")
                
                # حساب الحجم
                size = os.path.getsize(exe_path)
                size_mb = size / (1024 * 1024)
                print(f"📊 الحجم: {size_mb:.1f} MB")
                
                exe_found = True
                break
            else:
                print(f"⚠️ مجلد exe موجود لكن الملف التنفيذي مفقود: {exe_dir}")
else:
    print("❌ مجلد build لم يتم إنشاؤه")

# النتيجة النهائية
print("\n" + "=" * 50)

if exe_found:
    print("🎉 نجح! الملف التنفيذي تم إنشاؤه بنجاح")
    print("\n📋 كيفية الاستخدام:")
    print("1. اذهب إلى مجلد build/exe.xxx/")
    print("2. شغل GestionFormations.exe")
    print("3. البرنامج سيفتح في المتصفح")
    
    print("\n💡 للتوزيع:")
    print("- انسخ كامل مجلد build/exe.xxx/")
    print("- ليس فقط ملف .exe")
    
elif build_found:
    print("⚠️ مجلد build موجود لكن الملف التنفيذي مفقود")
    print("💡 قد تكون هناك أخطاء في التجميع")
    
else:
    print("❌ فشل في إنشاء الملف التنفيذي")
    print("💡 راجع الأخطاء أعلاه")

print("\n📞 للمساعدة:")
print("- شغل: python check_system.py للتشخيص")
print("- تأكد من تثبيت جميع المتطلبات")

input("\nاضغط Enter للخروج...")
