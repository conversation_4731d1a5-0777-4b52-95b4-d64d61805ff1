"""
Setup script pour créer un exécutable Windows de GESTION DES FORMATIONS
Utilise cx_Freeze pour créer un package standalone
"""

import sys
import os
from cx_Freeze import setup, Executable

# Dépendances à inclure
build_exe_options = {
    "packages": [
        "flask", "flask_sqlalchemy", "flask_login", "flask_wtf",
        "werkzeug", "jinja2", "wtforms", "sqlalchemy", "sqlite3",
        "datetime", "os", "shutil", "zipfile", "threading", "time",
        "secrets", "hashlib", "uuid", "json", "csv", "io", "base64"
    ],
    "include_files": [
        ("app/", "app/"),
        ("migrations/", "migrations/"),
        ("config.py", "config.py"),
        ("requirements.txt", "requirements.txt"),
        ("README.md", "README.md")
    ],
    "excludes": [
        "tkinter", "unittest", "email", "http", "urllib", "xml",
        "pydoc", "doctest", "argparse", "difflib"
    ],
    "optimize": 2,
    "include_msvcrt": True
}

# Configuration pour Windows
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # Pour une application GUI sans console

# Informations sur l'exécutable
executable = Executable(
    script="run.py",
    base=base,
    target_name="GestionFormations.exe",
    icon="app/static/images/Formation-continue-1024x1024.ico",  # Icône (à convertir en .ico)
    copyright="© 2024 ABOULFADEL.A - Tous droits réservés",
    trademarks="ABOULFADEL.A"
)

# Configuration du setup
setup(
    name="GESTION DES FORMATIONS",
    version="1.0.0",
    description="Système de gestion des formations - ABOULFADEL.A",
    author="ABOULFADEL.A",
    options={"build_exe": build_exe_options},
    executables=[executable]
)
