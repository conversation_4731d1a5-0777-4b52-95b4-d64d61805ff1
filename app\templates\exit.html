<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quitter l'Application - Gestion des Formations</title>
    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <style>
        body {
            background: url('{{ url_for("static", filename="images/grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg") }}') center center fixed;
            background-size: cover;
            min-height: 100vh;
            position: relative;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(26, 37, 47, 0.95) 100%);
            z-index: -1;
        }
        
        .exit-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .exit-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 60px 40px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0,0,0,0.3);
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .exit-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('{{ url_for("static", filename="images/Formation-continue-1024x1024.png") }}') center center no-repeat;
            background-size: 200px;
            opacity: 0.05;
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .exit-icon {
            font-size: 4rem;
            color: #e74c3c;
            margin-bottom: 30px;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .exit-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .exit-message {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .exit-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn-return {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            border: none;
            border-radius: 15px;
            padding: 18px 25px;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-align: center;
            min-height: 70px;
            line-height: 1.3;
            cursor: pointer;
        }

        .btn-return span, .btn-close span {
            line-height: 1.2;
        }

        .btn-return:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-close {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border: none;
            border-radius: 15px;
            padding: 18px 25px;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-align: center;
            min-height: 70px;
            line-height: 1.3;
            cursor: pointer;
        }

        .btn-close:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.4);
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
        }
        
        .exit-info {
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            color: #2980b9;
        }
        
        .exit-info i {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .company-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            border-radius: 50%;
            background: rgba(44, 62, 80, 0.1);
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        @media (max-width: 768px) {
            .exit-card {
                padding: 40px 30px;
                margin: 20px;
            }

            .exit-title {
                font-size: 2rem;
            }

            .exit-actions {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .btn-return, .btn-close {
                width: 100%;
                justify-content: center;
                padding: 15px 20px;
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <div class="exit-container">
        <div class="exit-card">
            <div class="exit-icon">
                <i class="fas fa-power-off"></i>
            </div>
            
            <h1 class="exit-title">Au revoir !</h1>
            
            <p class="exit-message">
                Merci d'avoir utilisé le système de gestion des formations.<br>
                Votre session a été fermée en toute sécurité.
            </p>
            
            <div class="exit-actions">
                <button type="button" class="btn-return">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Retourner à la<br>connexion</span>
                </button>

                <button type="button" class="btn-close">
                    <i class="fas fa-times"></i>
                    <span>Fermer la<br>fenêtre</span>
                </button>
            </div>
            
            <div class="exit-info">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Information :</strong><br>
                    Pour fermer complètement l'application, fermez cet onglet ou cette fenêtre du navigateur.
                </div>
            </div>
            
            <div class="company-info">
                <div class="company-logo">
                    <img src="{{ url_for('static', filename='images/Formation-continue-1024x1024.png') }}" alt="Logo">
                </div>
                <small class="text-muted">
                    Système de Gestion des Formations<br>
                    Version 1.0 - Développé avec sécurité
                </small>
            </div>
        </div>
    </div>

    <script>
        function closeWindow() {
            try {
                // Essayer de fermer la fenêtre
                window.close();

                // Si cela ne fonctionne pas, afficher un message après un délai
                setTimeout(function() {
                    if (!window.closed) {
                        alert('Pour fermer l\'application, fermez cet onglet ou cette fenêtre du navigateur.');
                    }
                }, 500);
            } catch (e) {
                alert('Pour fermer l\'application, fermez cet onglet ou cette fenêtre du navigateur.');
            }
        }

        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.exit-card');
            if (card) {
                card.style.opacity = '0';
                card.style.transform = 'scale(0.8) translateY(50px)';
                card.style.transition = 'all 0.8s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1) translateY(0)';
                }, 100);
            }

            // Ajouter des événements aux boutons pour s'assurer qu'ils fonctionnent
            const returnBtn = document.querySelector('.btn-return');
            const closeBtn = document.querySelector('.btn-close');

            if (returnBtn) {
                returnBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = "{{ url_for('login') }}";
                });
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeWindow();
                });
            }
        });

        // Redirection automatique après 30 secondes
        setTimeout(function() {
            if (confirm('Voulez-vous retourner à la page de connexion ?')) {
                window.location.href = "{{ url_for('login') }}";
            }
        }, 30000);
    </script>
</body>
</html>
