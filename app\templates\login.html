{% extends "base.html" %}

{% block login_content %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }
    .login-container {
        margin-top: 5%;
    }
    .login-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    .login-card .card-header {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        color: white;
        text-align: center;
        padding: 25px;
        border-bottom: none;
    }
    .login-card .card-body {
        padding: 40px;
    }
    .login-logo {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #fff;
    }
    .login-title {
        font-weight: 600;
        margin-bottom: 0;
    }
    .form-control {
        border-radius: 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
        border: 1px solid #e0e0e0;
        background-color: #f8f9fa;
    }
    .form-control:focus {
        box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
        border-color: #2c3e50;
    }
    .btn-login {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        border: none;
        border-radius: 10px;
        padding: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s;
    }
    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>

<div class="container login-container">
    <div class="row justify-content-center">
        <div class="col-md-5">
            <div class="card login-card">
                <div class="card-header">
                    <div class="login-logo">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="login-title">Gestion des Formations</h3>
                </div>
                <div class="card-body">
                    <h4 class="text-center mb-4">Connexion</h4>
                    <form method="POST" action="">
                        {{ form.hidden_tag() }}
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                {{ form.username(size=32, class="form-control", placeholder="Nom d'utilisateur") }}
                            </div>
                            {% for error in form.username.errors %}
                            <span class="text-danger">{{ error }}</span>
                            {% endfor %}
                        </div>
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                {{ form.password(size=32, class="form-control", placeholder="Mot de passe") }}
                            </div>
                            {% for error in form.password.errors %}
                            <span class="text-danger">{{ error }}</span>
                            {% endfor %}
                        </div>
                        <div class="mb-4 form-check">
                            {{ form.remember_me(class="form-check-input") }}
                            {{ form.remember_me.label(class="form-check-label") }}
                        </div>
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-login btn-primary") }}
                        </div>

                        <div class="text-center mt-3">
                            <a href="{{ url_for('forgot_password') }}" class="text-decoration-none">
                                <i class="fas fa-unlock-alt"></i> نسيت كلمة المرور؟
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
