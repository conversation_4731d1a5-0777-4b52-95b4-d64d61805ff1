{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-key"></i> تغيير كلمة المرور</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-group mb-3">
                            {{ form.current_password.label(class="form-control-label") }}
                            {{ form.current_password(class="form-control", placeholder="أدخل كلمة المرور الحالية") }}
                            {% if form.current_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.current_password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            {{ form.new_password.label(class="form-control-label") }}
                            {{ form.new_password(class="form-control", placeholder="أدخل كلمة المرور الجديدة") }}
                            {% if form.new_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.new_password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                        </div>
                        
                        <div class="form-group mb-4">
                            {{ form.confirm_password.label(class="form-control-label") }}
                            {{ form.confirm_password(class="form-control", placeholder="أعد إدخال كلمة المرور الجديدة") }}
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                        </div>
                    </form>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة إلى لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    padding: 20px;
}

.card-body {
    padding: 30px;
}

.form-control {
    border-radius: 10px;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    background-color: #fff;
}

.btn {
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108,117,125,0.3);
}

.form-control-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.text-muted {
    color: #6c757d !important;
    font-size: 0.875rem;
}
</style>
{% endblock %}
