"""
Script de test pour vérifier le téléchargement des sauvegardes
"""

import os
import requests
import sys

def test_download():
    """Tester le téléchargement d'une sauvegarde"""
    
    print("🧪 TEST DE TÉLÉCHARGEMENT DES SAUVEGARDES")
    print("=" * 50)
    
    # URL de base (modifiez selon votre configuration)
    base_url = "http://127.0.0.1:5000"
    
    print(f"🌐 URL de base: {base_url}")
    
    # Créer une session pour maintenir les cookies
    session = requests.Session()
    
    try:
        # 1. Tester la connexion
        print("\n1. Test de connexion...")
        response = session.get(f"{base_url}/login")
        if response.status_code == 200:
            print("✅ Serveur accessible")
        else:
            print(f"❌ Erreur de connexion: {response.status_code}")
            return
        
        # 2. Se connecter (vous devrez adapter selon vos identifiants)
        print("\n2. Tentative de connexion...")
        login_data = {
            'username': 'admin',
            'password': 'admin123',
            'csrf_token': 'test'  # Vous devrez récupérer le vrai token
        }
        
        response = session.post(f"{base_url}/login", data=login_data)
        print(f"Statut de connexion: {response.status_code}")
        
        # 3. Accéder à la liste des sauvegardes
        print("\n3. Accès à la liste des sauvegardes...")
        response = session.get(f"{base_url}/backup/list")
        if response.status_code == 200:
            print("✅ Liste des sauvegardes accessible")
            
            # Chercher des liens de téléchargement dans la réponse
            if "download_backup" in response.text:
                print("✅ Liens de téléchargement trouvés")
            else:
                print("⚠️ Aucun lien de téléchargement trouvé")
        else:
            print(f"❌ Erreur d'accès: {response.status_code}")
        
        print("\n📋 RECOMMANDATIONS:")
        print("- Vérifiez que le serveur Flask est démarré")
        print("- Vérifiez vos identifiants de connexion")
        print("- Assurez-vous qu'il y a des sauvegardes dans la base")
        print("- Exécutez diagnostic_backups.py pour plus de détails")
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur")
        print("💡 Assurez-vous que l'application Flask est démarrée")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def check_backup_folder():
    """Vérifier le dossier de sauvegardes"""
    
    print("\n📁 VÉRIFICATION DU DOSSIER DE SAUVEGARDES")
    print("=" * 50)
    
    backup_dirs = ['backups', 'app/backups', '../backups']
    
    for backup_dir in backup_dirs:
        if os.path.exists(backup_dir):
            files = [f for f in os.listdir(backup_dir) if f.endswith(('.db', '.zip'))]
            print(f"✅ {backup_dir}: {len(files)} fichiers")
            
            for file in files[:5]:  # Afficher les 5 premiers
                file_path = os.path.join(backup_dir, file)
                size = os.path.getsize(file_path)
                print(f"   📄 {file} ({size} bytes)")
            
            if len(files) > 5:
                print(f"   ... et {len(files) - 5} autres fichiers")
        else:
            print(f"❌ {backup_dir}: n'existe pas")

def main():
    """Fonction principale"""
    
    print("🔧 TEST DE TÉLÉCHARGEMENT - GESTION DES FORMATIONS")
    print("Développé par ABOULFADEL.A")
    print("=" * 60)
    
    check_backup_folder()
    test_download()
    
    print("\n" + "=" * 60)
    print("Test terminé!")

if __name__ == "__main__":
    main()
