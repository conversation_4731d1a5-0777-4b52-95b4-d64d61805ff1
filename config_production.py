"""
Configuration de production pour GESTION DES FORMATIONS
Optimisée pour l'utilisation en réseau multi-utilisateurs
"""

import os
import secrets

class ProductionConfig:
    """Configuration pour l'environnement de production"""
    
    # Sécurité
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 heure
    
    # Base de données
    basedir = os.path.abspath(os.path.dirname(__file__))
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'app', 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'check_same_thread': False,  # Important pour SQLite multi-threading
            'timeout': 30
        }
    }
    
    # Upload de fichiers
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB max
    UPLOAD_FOLDER = os.path.join(basedir, 'uploads')
    
    # Sauvegardes
    BACKUP_FOLDER = os.path.join(basedir, 'backups')
    
    # Logs
    LOG_FOLDER = os.path.join(basedir, 'logs')
    
    # Performance
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1 an pour les fichiers statiques
    
    # Multi-utilisateurs
    THREADED = True
    PROCESSES = 1
    
    # Sécurité réseau
    SESSION_COOKIE_SECURE = False  # False pour HTTP local
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = 3600  # 1 heure
    
    @staticmethod
    def init_app(app):
        """Initialiser l'application avec cette configuration"""
        
        # Créer les dossiers nécessaires
        folders = [
            ProductionConfig.UPLOAD_FOLDER,
            ProductionConfig.BACKUP_FOLDER,
            ProductionConfig.LOG_FOLDER
        ]
        
        for folder in folders:
            if not os.path.exists(folder):
                os.makedirs(folder)
        
        # Configuration des logs
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            # Log des erreurs
            error_log = os.path.join(ProductionConfig.LOG_FOLDER, 'errors.log')
            error_handler = RotatingFileHandler(
                error_log, maxBytes=10240000, backupCount=10
            )
            error_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            error_handler.setLevel(logging.ERROR)
            app.logger.addHandler(error_handler)
            
            # Log général
            info_log = os.path.join(ProductionConfig.LOG_FOLDER, 'app.log')
            info_handler = RotatingFileHandler(
                info_log, maxBytes=10240000, backupCount=10
            )
            info_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s'
            ))
            info_handler.setLevel(logging.INFO)
            app.logger.addHandler(info_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('GESTION DES FORMATIONS - Démarrage en mode production')

# Configuration par défaut
Config = ProductionConfig
