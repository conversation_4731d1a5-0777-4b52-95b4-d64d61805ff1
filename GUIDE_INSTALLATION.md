# 📦 GUIDE DE CRÉATION DE L'INSTALLATEUR
## GESTION DES FORMATIONS - ABOULFADEL.A

---

## 🎯 OBJECTIF
Créer un installateur Windows (.exe) standalone pour **GESTION DES FORMATIONS** qui fonctionne sur:
- ✅ Windows 7, 8, 8.1, 10, 11
- ✅ Architecture 32-bit et 64-bit
- ✅ Multi-utilisateurs en réseau (LAN)
- ✅ Sans dépendances externes

---

## 🛠️ PRÉREQUIS

### 1. Logiciels requis:
```bash
# Python 3.8+ (recommandé: 3.11)
https://www.python.org/downloads/

# Inno Setup (pour créer l'installateur)
https://jrsoftware.org/isinfo.php
```

### 2. Dépendances Python:
```bash
pip install Flask Flask-SQLAlchemy Flask-Login Flask-WTF
pip install Pillow cx_Freeze
```

---

## 🚀 ÉTAPES DE CRÉATION

### Étape 1: Préparation
```bash
# Exécuter le script de préparation
python prepare_build.py
```

### Étape 2: Build automatique
```bash
# Exécuter le script de build complet
build_all.bat
```

### Étape 3: Création de l'installateur
```bash
# Après installation d'Inno Setup
iscc installer_script.iss
```

---

## 📁 STRUCTURE DES FICHIERS CRÉÉS

```
📦 Projet/
├── 🔧 Scripts de build/
│   ├── setup.py                 # Configuration cx_Freeze
│   ├── build_installer.py       # Générateur script Inno Setup
│   ├── build_all.bat           # Script automatique complet
│   ├── prepare_build.py        # Préparation et vérifications
│   └── run_server.py           # Serveur multi-utilisateurs
│
├── ⚙️ Configuration/
│   ├── config_production.py    # Config optimisée production
│   ├── requirements.txt        # Dépendances Python
│   ├── LICENSE.txt             # Licence d'utilisation
│   └── README.txt              # Guide utilisateur final
│
├── 📦 Build/
│   ├── build/                  # Exécutable généré
│   ├── dist/installer/         # Installateur final
│   └── installer_script.iss    # Script Inno Setup
│
└── 🎨 Ressources/
    └── app/static/images/
        ├── Formation-continue-1024x1024.png
        └── Formation-continue-1024x1024.ico
```

---

## 🌐 FONCTIONNALITÉS MULTI-UTILISATEURS

### Configuration Serveur:
- **Port par défaut**: 5000
- **Accès local**: http://127.0.0.1:5000
- **Accès réseau**: http://[IP_SERVEUR]:5000

### Utilisation:
1. **Ordinateur serveur**: Installer et lancer le programme
2. **Autres ordinateurs**: Ouvrir navigateur → http://IP_SERVEUR:5000
3. **Identifiants par défaut**: admin / admin123

---

## 🔐 SÉCURITÉ ET PERFORMANCE

### Optimisations incluses:
- ✅ Support multi-threading
- ✅ Pool de connexions SQLite
- ✅ Gestion des sessions sécurisée
- ✅ Logs rotatifs automatiques
- ✅ Sauvegarde automatique avant import

### Dossiers créés automatiquement:
- `backups/` - Sauvegardes de la base de données
- `uploads/` - Fichiers uploadés par les utilisateurs
- `logs/` - Journaux d'activité et erreurs

---

## 📋 CHECKLIST DE VALIDATION

### ✅ Avant le build:
- [ ] Python 3.8+ installé
- [ ] Toutes les dépendances installées
- [ ] Image logo présente dans app/static/images/
- [ ] Tests de l'application réussis

### ✅ Après le build:
- [ ] Exécutable créé dans build/
- [ ] Test de lancement de l'exécutable
- [ ] Vérification accès multi-utilisateurs
- [ ] Test sur différentes versions Windows

### ✅ Installateur final:
- [ ] Inno Setup installé
- [ ] Compilation du script .iss réussie
- [ ] Test d'installation sur machine propre
- [ ] Vérification désinstallation

---

## 🆘 RÉSOLUTION DE PROBLÈMES

### Erreur: "Module not found"
```bash
# Réinstaller les dépendances
pip uninstall -y Flask Flask-SQLAlchemy Flask-Login Flask-WTF
pip install -r requirements.txt
```

### Erreur: "Icon file not found"
```bash
# Reconvertir l'icône
python prepare_build.py
```

### Erreur: "Database locked"
```bash
# Vérifier que l'application n'est pas déjà lancée
# Redémarrer et relancer le build
```

---

## 📞 SUPPORT

**Développeur**: ABOULFADEL.A  
**Version**: 1.0.0  
**Date**: 2024

Pour toute assistance technique, contactez le développeur.

---

## 📄 LICENCE

© 2024 ABOULFADEL.A - Tous droits réservés

Ce logiciel est la propriété exclusive de ABOULFADEL.A.
Utilisation autorisée selon les termes de la licence incluse.
